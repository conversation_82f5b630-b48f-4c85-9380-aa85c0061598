#!/usr/bin/env python3
"""
测试项目配置搜索功能的脚本
"""

import json

def test_category_parsing():
    """测试分类解析逻辑"""
    # 模拟 PROJECT_CATEGORIES 配置
    categories_json = '''[
        {
            "key": "approval",
            "title": "申报审批",
            "modules": [
                {"key": "DOC_GEN", "title": "项目申报书生成"}
            ]
        },
        {
            "key": "office",
            "title": "日常办公",
            "modules": [
                {"key": "MEETING_GEN", "title": "会议纪要"}
            ]
        },
        {
            "key": "research",
            "title": "研究分析",
            "modules": [
                {"key": "MARKET_INVESTMENT", "title": "市场行业投资研习报告"},
                {"key": "FEASIBILITY", "title": "可行性研究报告"},
                {"key": "THINK_TANK", "title": "智库报告"}
            ]
        }
    ]'''
    
    # 测试分类过滤逻辑
    categories = "approval,research"
    category_list = [cat.strip() for cat in categories.split(',') if cat.strip()]
    print(f"输入分类: {categories}")
    print(f"解析后分类列表: {category_list}")
    
    try:
        categories_data = json.loads(categories_json)
        module_keys = []
        for category_data in categories_data:
            if category_data.get("key") in category_list:
                print(f"匹配分类: {category_data.get('key')} - {category_data.get('title')}")
                for module in category_data.get("modules", []):
                    module_keys.append(module.get("key"))
                    print(f"  模块: {module.get('key')} - {module.get('title')}")
        
        print(f"最终模块keys: {module_keys}")
        return module_keys
        
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return []

def test_name_search():
    """测试名称搜索逻辑"""
    test_names = [
        "智能交通系统研究",
        "人工智能在医疗领域的应用",
        "区块链技术发展报告",
        "新能源汽车市场分析"
    ]
    
    keyword = "智能"
    print(f"\n搜索关键词: {keyword}")
    print("匹配的项目名称:")
    
    for name in test_names:
        if keyword in name:
            print(f"  ✓ {name}")
        else:
            print(f"  ✗ {name}")

if __name__ == "__main__":
    print("=== 测试项目配置搜索功能 ===")
    
    print("\n1. 测试分类过滤逻辑:")
    test_category_parsing()
    
    print("\n2. 测试名称搜索逻辑:")
    test_name_search()
    
    print("\n=== 测试完成 ===")
