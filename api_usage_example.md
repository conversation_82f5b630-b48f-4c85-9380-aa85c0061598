# 项目配置分页查询API使用示例

## API 端点
```
GET /api/project-configs/list/paginated
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，从1开始，默认1 |
| size | int | 否 | 每页数量，默认10 |
| status | string | 否 | 项目状态过滤 |
| keyword | string | 否 | 课题名称模糊搜索关键词 |
| categories | array[string] | 否 | 项目分类列表 |

## 使用示例

### 1. 基础分页查询
```http
GET /api/project-configs/list/paginated?page=1&size=10
```

### 2. 按课题名称搜索
```http
GET /api/project-configs/list/paginated?keyword=智能交通
```

### 3. 按分类筛选
```http
GET /api/project-configs/list/paginated?categories=approval&categories=research
```

### 4. 组合查询
```http
GET /api/project-configs/list/paginated?page=1&size=20&keyword=人工智能&categories=research&status=CONFIGURING
```

## 分类配置说明

系统支持的分类包括：

- **approval** (申报审批)
  - DOC_GEN: 项目申报书生成

- **office** (日常办公)  
  - MEETING_GEN: 会议纪要

- **research** (研究分析)
  - MARKET_INVESTMENT: 市场行业投资研习报告
  - FEASIBILITY: 可行性研究报告
  - THINK_TANK: 智库报告

- **education** (学术教育)
  - LITERATURE_REVIEW: 文献综述
  - CLASS_NOTES: 课堂笔记

- **business** (商业文档)

- **technical** (技术文档)

## 查询逻辑

1. **课题名称搜索**: 对 `name` 字段进行模糊匹配（不区分大小写）
2. **分类筛选**: 
   - 根据传入的分类列表，从系统配置中获取对应的模块keys
   - 使用 `doc_type` 字段进行精确匹配（注意：该字段目前还未在数据库中创建）

## 响应格式

```json
{
  "success": true,
  "code": 0,
  "data": {
    "items": [
      {
        "id": "uuid",
        "name": "项目名称",
        "application_category": "分类",
        "status": "状态",
        "created_at": "创建时间",
        "updated_at": "更新时间",
        // ... 其他字段
      }
    ],
    "total": 100,
    "page": 1,
    "size": 10
  },
  "error": ""
}
```

## 注意事项

1. `doc_type` 字段目前还未在 ProjectConfig 模型中创建，需要后续添加
2. 当前实现中，分类筛选使用的是 `doc_type` 字段，如果该字段不存在，查询可能会失败
3. 建议在添加 `doc_type` 字段后进行完整测试
