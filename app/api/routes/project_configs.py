from typing import List, Optional, Union
from app.core.logging import get_logger
from fastapi import APIRouter, Depends, status, Body, Request, Query, UploadFile, File, HTTPException
from datetime import datetime
from app.models.organization_model_use import UseCase
from app.api.repository.user_default_model import get_user_model
from app.api.deps import get_current_user_from_state
from app.api.schemas.user import UserResponse
from app.models.project_configs import ProjectConfig
from app.models.project_leaders import ProjectLeader
from app.models.research import Research
from app.models.project_member_joins import ProjectMemberJoin
from app.api.schemas.project_configs import (
    ProjectConfigCreate,
    ProjectConfigUpdate,
    ProjectConfigResponse,
    ProjectConfigResponse2,
    LanguageStyle,
    LanguageStyleText,
    LiteratureLibraryType,
    LiteratureLibraryTypeText,
    LanguageStyleResponse,
    LiteratureLibraryResponse,
    ProjectConfigStatus,
    GenerateLeaderAI,
    ProjectConfigDemoResponse,
    ProjectCategory,
    ProjectCategoryModule
)
from app.api.schemas.project_member_joins import ProjectMemberJoinResponse
from app.utils.utils import (
    send_data,
    ResponseModel,
    PageQuery,
    send_page_data,
    ResponsePageModel,
    save_text_to_file,
    sanitize_filename,
    handle_before_save,
    docx_file_to_markdown
)
from app.api.routes.workflow import create_workflow
from app.models.model_config import ModelConfig
from app.services.product_configs_service import ProductConfigsService
from app.models.requirements_attachments_files import RequirementsAttachmentFiles
from app.api.schemas.role import InsetRole
from app.api.repository.project_config import (
  generate_leader_ai_introduction,
  get_one_project_config
)
from app.api.repository.project_url_summary import bind_project
from app.api.repository.upload_file import save_upload_file, get_file_content_by_id
from app.api.schemas.upload_file import UploadFileResponse
import os
from app.utils.enum import ProjectConfigError, UploadFileError
from app.services.system_config_service import system_config_service
import json

logger = get_logger(__name__)
router = APIRouter()

async def get_project_configs(
  current_user: UserResponse,
  is_deleted: Optional[int] = 0,
  status: Optional[str] = None,
) -> list[ProjectConfigResponse2]:
    """获取所有项目配置（管理员接口）"""
    # 预加载基本数据
    query = ProjectConfig.all().prefetch_related(
        "user",
        "leader",
        "model",
        "user__role",
        "user__organization",
        "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district"
    ).order_by("-updated_at")
    configs = []
    if current_user.role.identifier != InsetRole.SUPER_ADMIN:
        configs = query.filter(is_deleted=is_deleted, user=current_user.id)
    else:
        configs = query.filter(is_deleted=is_deleted)
    if status:
        configs = await query.filter(status=status).all()
    else:
        configs = await query.all()
    result = []
    for config in configs:
        config_dict = ProjectConfigResponse.model_validate(config, from_attributes=True).model_dump()
        
        # 查询所有相关的团队成员
        team_members = []
        if config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        result.append(ProjectConfigResponse2.model_validate(config_dict))
    return result

# 管理员接口
# 管理员获取项目配置列表
@router.get("/list", response_model=ResponseModel[List[ProjectConfigResponse2]])
async def read_project_configs_admin(
    request: Request,
    is_deleted: Optional[int] = 0,
    status: Optional[str] = None
):
    current_user = get_current_user_from_state(request)
    result = await get_project_configs(is_deleted=is_deleted, status=status, current_user=current_user)
    return send_data(True, result)

# 管理员获取指定项目配置
@router.get("/{project_id}", response_model=ResponseModel[ProjectConfigResponse2])
async def read_project_config_admin(
    project_id: str,
    request: Request
):
    try:
        current_user = get_current_user_from_state(request)
        result = await get_one_project_config(project_id)
        if result is None:
            return send_data(False, None, "项目配置不存在")
        if (result.user.id != current_user.id
            and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
        ):
            return send_data(False, None, "无权访问此项目")
        return send_data(True, result)
    except Exception as e:
        return send_data(True, None, str(e))

# 管理员创建项目配置
@router.post("", response_model=ResponseModel[ProjectConfigResponse2], status_code=status.HTTP_201_CREATED)
async def create_project_config_admin(
    request: Request,
    config_in: ProjectConfigCreate = None
):
    """创建新的项目配置（管理员接口）"""
    current_user = get_current_user_from_state(request)
    try:
        # 创建项目配置
        config_data = config_in.model_dump() if config_in else {"is_deleted": 0}
        
        # 从数据中移除外键引用，避免将字符串赋值给外键字段
        leader_id = None
        if "leader" in config_data:
            leader_id = config_data.pop("leader")
            
        # team_members_id = None
        # if "team_members" in config_data:
        #     team_members_id = config_data.pop("team_members")
        model_id = None
        if "model" in config_data:
            model_id = config_data.pop("model")
        research_id = None
        if "research" in config_data:
            research_id = config_data.pop("research")    
        # # 先移除 requirements_attachments_id，等 config 保存后再处理
        requirements_attachments_id = None
        if "requirements_attachments_id" in config_data:
            requirements_attachments_id = config_data.pop("requirements_attachments_id")
            
        config = await ProjectConfig.create(
          **config_data,
          leader_id=leader_id,
          research_id=research_id,
          model_id=model_id,
          user_id=current_user.id,
          status=ProjectConfigStatus.CONFIGURING.value
        )
        logger.info(f"config_data: {config_data}")

        # 处理外键关系
        if config_in.url_ids:
            await bind_project(
                url_list=config_in.url_ids,
                project_id=config.id
            )
        # if leader_id:
        #     leader = await ProjectLeader.filter(id=leader_id).first()
        #     if leader:
        #         config.leader = leader
        #         await config.save()
        # if model_id:
        #     model = await ModelConfig.filter(id=model_id).first()
        #     if model:
        #         config.model = model
        #         await config.save()     
        # if team_members_id:
        #     # 对于team_members，只需要保存ID字符串，因为它是CharField而不是ForeignKey
        #     config.team_members = team_members_id
        #     await config.save()
        # if research_id:
        #     research = await Research.filter(id=research_id).first()
        #     if research:
        #         config.research = research
        #         await config.save()    
        # 处理 requirements_attachments_id - 使用新的绑定机制
        if requirements_attachments_id:
            # 导入绑定相关的模块
            from app.models.file_biz_relations import FileBizRelation
            from app.utils.constants import ProductType
            
            # 验证文件是否存在且未被删除
            requirements_attachments_files = await RequirementsAttachmentFiles.filter(
                id__in=requirements_attachments_id, 
                is_deleted=False
            ).all()
            if requirements_attachments_files:
                logger.info(f"开始为项目 {config.id} 绑定 {len(requirements_attachments_files)} 个文件")
                
                analysis_results = []
                for file in requirements_attachments_files:
                    # 创建参考资料关联记录
                    relation = await FileBizRelation.create(
                        file_id=file.id,
                        ai_analysis_summary=file.analysis_result,
                        product_type=ProductType.DOCGEN,  # 材料生成产品
                        biz_id=str(config.id)
                    )
                    logger.info(f"创建参考资料关联: 文件ID {file.id} -> DOCGEN:{config.id}")
                    
                    # 更新文件的项目关联
                    file.project_configs = config
                    await file.save()
                    await relation.save()
                    
                    # 收集分析结果
                    if file.analysis_result:
                        analysis_results.append(file.analysis_result)
                
                # 将分析结果组装成指定格式并存储
                config.requirements_attachments = analysis_results
                await config.save()  # 确保保存更新后的config
                logger.info(f"项目 {config.id} 成功绑定参考资料，分析结果数量: {len(analysis_results)}")

        result = await get_one_project_config(config.id)
        
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, f"创建项目配置失败: {str(e)}")

# 管理员更新项目配置
@router.put("/{project_id}", response_model=ResponseModel[ProjectConfigResponse2])
async def update_project_config_admin(
    project_id: str, config_in: ProjectConfigUpdate
):
    """更新项目配置（管理员接口）"""
    config = await ProjectConfig.filter(id=project_id).first()
    if config is None:
        return send_data(False, None, "项目配置不存在")
    
    try:
        # 处理更新字段，只更新用户提交的字段
        update_data = config_in.model_dump(exclude_unset=True, exclude_none=True)
        
        name = update_data.get("name")
        application_category = update_data.get("application_category")
        if config.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
            return send_data(False, None, "大纲生成中，无法修改")
        if config.status == ProjectConfigStatus.REPORT_GENERATING.value:
            return send_data(False, None, "报告生成中，无法修改")
        # 验证名称和申报口径是否可以更改
        if name and name != config.name and config.status != ProjectConfigStatus.CONFIGURING.value:
            return send_data(False, None, "研究主体不可以更改")
        if application_category and application_category != config.application_category and config.status != ProjectConfigStatus.CONFIGURING.value:
            return send_data(False, None, "申报口径不可以更改")
        # 验证外键关联是否存在
        if "leader" in update_data:
            leader_id = update_data.pop("leader")
            if leader_id:
                leader = await ProjectLeader.filter(id=leader_id).first()
                if not leader:
                    return send_data(False, None, "指定的申报主体不存在")
                config.leader = leader
            else:
                config.leader = None
        if "url_ids" in update_data:
            await bind_project(
                url_list=update_data["url_ids"],
                project_id=project_id
            )
        # 验证外键关联是否存在
        if "user_add_demo_id" in config_in:
            demo_id = update_data.pop("user_add_demo_id")
            if demo_id:
                demo_data = await get_file_content_by_id(demo_id)
                if not demo_data:
                    return send_data(False, None, UploadFileError.NOT_RECORD.value)
                else:
                    config.user_add_demo = demo_data
            else:
                config.user_add_demo = None
        if "model" in update_data:
            model_id = update_data.pop("model")
            if model_id:
                model = await ModelConfig.filter(id=model_id).first()
                if not model:
                    return send_data(False, None, "指定的模型配置不存在")
                config.model = model
            else:
                config.model = None
        if "research" in update_data:
            research_id = update_data.pop("research")
            if research_id:
                research = await Research.filter(id=research_id).first()
                if not research:
                    return send_data(False, None, "指定的模型配置不存在")
                config.research = research
            else:
                config.research = None
        if "team_members" in update_data:
            team_members_id = update_data["team_members"]
            if team_members_id:
                # 验证team_members_id是否存在
                exists = await ProjectMemberJoin.filter(join_id=team_members_id).exists()
                if not exists:
                    return send_data(False, None, "指定的团队成员关联ID不存在")
            # 不需要删除team_members，直接更新字符串字段

        # 如果更新了is_deleted字段为1，设置deleted_at时间
        if "is_deleted" in update_data and update_data["is_deleted"] == 1:
            update_data["deleted_at"] = datetime.now()
        
        # 处理 requirements_attachments_id - 先提取出来
        requirements_attachments_id = None
        if "requirements_attachments_id" in update_data:
            requirements_attachments_id = update_data.pop("requirements_attachments_id")
        
        # 排除AI生成内容相关字段，这些字段应由专门的生成接口更新
        # 注意：status字段需要特殊处理，确保使用正确的枚举值
        special_fields = ["ai_generated_outline", "manual_modified_outline", "ai_generated_report", "status"]
        for field in special_fields:
            if field in update_data:
                del update_data[field]
        
        # 更新其他常规字段
        for key, value in update_data.items():
            setattr(config, key, value)
        
        await config.save()
        
        # 在 config 保存后处理 requirements_attachments_id - 使用新的绑定机制
        if requirements_attachments_id:
            # 导入绑定相关的模块
            from app.models.file_biz_relations import FileBizRelation
            from app.utils.constants import ProductType
            
            # 先清理该项目现有的参考资料关联
            await FileBizRelation.filter(
                product_type=ProductType.DOCGEN,
                biz_id=str(config.id)
            ).delete()
            logger.info(f"已清理项目 {config.id} 的现有参考资料关联")
            
            # 查询所有指定的附件文件（只查询未删除的）
            requirements_attachments_files = await RequirementsAttachmentFiles.filter(
                id__in=requirements_attachments_id, 
                is_deleted=False
            ).all()
            if requirements_attachments_files:
                logger.info(f"开始为项目 {config.id} 重新绑定 {len(requirements_attachments_files)} 个参考资料文件")
                
                analysis_results = []
                for file in requirements_attachments_files:
                    # 创建参考资料关联记录
                    relation = await FileBizRelation.create(
                        file_id=file.id,
                        ai_analysis_summary=file.analysis_result,
                        product_type=ProductType.DOCGEN,  # 材料生成产品
                        biz_id=str(config.id)
                    )
                    logger.info(f"重新创建参考资料关联: 文件ID {file.id} -> DOCGEN:{config.id}")
                    
                    # 更新文件的项目关联
                    file.project_configs = config
                    await file.save()
                    await relation.save()
                    
                    # 收集分析结果
                    if file.analysis_result:
                        analysis_results.append(file.analysis_result)
                
                # 将分析结果组装成指定格式并存储
                config.requirements_attachments = analysis_results
                await config.save()  # 确保保存更新后的config
                logger.info(f"项目 {config.id} 成功重新绑定参考资料，分析结果数量: {len(analysis_results)}")

       

        # 查询更新后的配置
        updated_config = await ProjectConfig.filter(id=project_id).prefetch_related(
            "user", "leader", "model",
            "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization"
        ).first()
        
        config_dict = ProjectConfigResponse.model_validate(updated_config, from_attributes=True).model_dump()
        
        # 获取所有相关的团队成员
        team_members = []
        if updated_config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=updated_config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        
        return send_data(True, ProjectConfigResponse2.model_validate(config_dict))
    except Exception as e:
        return send_data(False, None, f"更新项目配置失败: {str(e)}")

# 管理员软删除项目配置
@router.delete("/{project_id}", response_model=ResponseModel[ProjectConfigResponse2])
async def soft_delete_project_config_admin(
    project_id: str,
    request: Request
):
    """软删除项目配置（管理员接口）"""
    current_user = get_current_user_from_state(request)
    config = await ProjectConfig.filter(id=project_id).first()
    if config is None:
        return send_data(False, None, "项目配置不存在")
    if (config.user_id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]    
    ):
        return send_data(False, None, "权限不足")
    if config.status == ProjectConfigStatus.OUTLINE_GENERATING.value:
        return send_data(False, None, "大纲生成中，无法删除")
    if config.status == ProjectConfigStatus.REPORT_GENERATING.value:
        return send_data(False, None, "报告生成中，无法删除")
    try:
        # 标记为已删除
        config.is_deleted = 1
        config.deleted_at = datetime.now()
        await config.save()
        
        # 查询已删除的配置
        deleted_config = await ProjectConfig.filter(id=project_id).prefetch_related(
            "user",
            "leader",
            "model",
            "leader__province",  # leader 的外键字段
            "leader__city",
            "leader__district",
            "user__role",
            "user__organization"
        ).first()
        
        config_dict = ProjectConfigResponse.model_validate(deleted_config, from_attributes=True).model_dump()
        
        # 获取所有相关的团队成员
        team_members = []
        if deleted_config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=deleted_config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        
        return send_data(True, ProjectConfigResponse2.model_validate(config_dict))
    except Exception as e:
        return send_data(False, None, f"删除项目配置失败: {str(e)}")

# 更新手动修改大纲接口
@router.post("/{project_id}/update-manual-outline", response_model=ResponseModel[ProjectConfigResponse])
async def update_manual_outline(
    request: Request,
    project_id: str, 
    manual_modified_outline: str = Body(..., embed=True)
):
    """手动更新项目大纲内容"""
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related(
        "leader",
        "user",
        "model",
        "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization",
    ).first()
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]
    ):
        return send_data(False, None, "无权访问此项目")
    
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"manual_outline_{timestamp}.txt"
    if config_db.name:
      file_name = sanitize_filename(f"manual_outline_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = config_db.manual_modified_outline or f"{project_folder}/{file_name}"
    
    # # 确保目录存在
    # abs_folder_path = os.path.join(os.getcwd(), project_folder)
    # if not os.path.exists(abs_folder_path):
    #     os.makedirs(abs_folder_path)
    
    # # 获取绝对路径
    # abs_file_path = os.path.join(os.getcwd(), relative_path)
    
    # # 保存内容到文件
    # with open(abs_file_path, "w", encoding="utf-8") as f:
    #   f.write(manual_modified_outline)
    save_text_to_file(
        content=handle_before_save(manual_modified_outline),
        file_path=relative_path
    )
    # 更新项目配置
    config_db.manual_modified_outline = relative_path
    config_db.manual_modified_outline_time = datetime.now()
    config_db.ai_generated_report = None
    config_db.report_generation_time = None
    config_db.manual_modified_report_time = None
    config_db.manual_modified_report = None
    await config_db.save()
    await create_workflow(
        project_id = project_id,
        name="OUTLINE_POLISHING",
        current_user=current_user,
        content=relative_path
    )
    return send_data(True, ProjectConfigResponse.model_validate(config_db, from_attributes=True))
# 更新修改报告接口
@router.post("/{project_id}/update-report", response_model=ResponseModel[ProjectConfigResponse])
async def update_report(
    request: Request,
    project_id: str, 
    manual_modified_report: str = Body(..., embed=True)
):
    """手动更新报告的内容"""
    # 检查项目是否存在并验证权限
    current_user = get_current_user_from_state(request)
    config_db = await ProjectConfig.filter(id=project_id).prefetch_related(
        "leader",
        "user",
        "model",
        "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization"
    ).first()
    if not config_db:
        return send_data(False, None, "项目配置不存在")
    
    if (config_db.user.id != current_user.id
        and current_user.role.identifier not in [InsetRole.SUPER_ADMIN, InsetRole.ADMIN]    
    ):
        return send_data(False, None, "无权访问此项目")
    
    # 准备文件路径
    project_folder = f"llm_file/{project_id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = f"manual_report_{timestamp}.txt"
    if config_db.name:
      file_name = sanitize_filename(f"manual_report_{config_db.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    
    # # 确保目录存在
    # abs_folder_path = os.path.join(os.getcwd(), project_folder)
    # if not os.path.exists(abs_folder_path):
    #     os.makedirs(abs_folder_path)
    
    # # 获取绝对路径
    # abs_file_path = os.path.join(os.getcwd(), relative_path)
    
    # # 保存内容到文件
    # with open(abs_file_path, "w", encoding="utf-8") as f:
    #   f.write(manual_modified_report)
    save_text_to_file(
        content=handle_before_save(manual_modified_report),
        file_path=relative_path
    )
    # 更新项目配置
    config_db.manual_modified_report = relative_path
    config_db.manual_modified_report_time = datetime.now()
    await config_db.save()
    await create_workflow(
        project_id = project_id,
        name="CONTENT_POLISHING",
        current_user=current_user,
        content=relative_path
    )
    
    return send_data(True, ProjectConfigResponse.model_validate(config_db, from_attributes=True))

@router.get("/user/latest", response_model=ResponseModel[ProjectConfigResponse2], deprecated=True)
async def get_latest_project_config(
    request: Request
):
    """获取用户最近创建的项目配置"""
    current_user = get_current_user_from_state(request)
    try:
        # 查询用户最近创建的项目配置
        config = await ProjectConfig.filter(
            user_id=current_user.id,
            is_deleted=0
        ).order_by("-created_at").prefetch_related("leader", "user", "model", "leader__province",  # leader 的外键字段
        "leader__city",
        "leader__district",
        "user__role",
        "user__organization").first()
        
        if not config:
            return send_data(False, None, "未找到项目配置")
            
        # 获取所有相关的团队成员
        team_members = []
        if config.team_members:
            team_members = await ProjectMemberJoin.filter(
                join_id=config.team_members,
                is_deleted=0
            ).prefetch_related("member").all()
            
        # 构建响应数据
        config_dict = ProjectConfigResponse.model_validate(config, from_attributes=True).model_dump()
        config_dict["team_members"] = [
            ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
            for tm in team_members
        ]
        
        return send_data(True, ProjectConfigResponse2.model_validate(config_dict))
    except Exception as e:
        return send_data(False, None, f"获取最近项目配置失败: {str(e)}")

# 获取语言风格的枚举值
@router.get("/dict/language-style", response_model=ResponseModel[List[LanguageStyleResponse]])
async def get_language_style():
    """获取语言风格的枚举值"""
    language_styles = [LanguageStyleResponse(value=style.value, label=LanguageStyleText[style.value]) for style in LanguageStyle]
    return send_data(True, language_styles)

# 获取文献库的枚举值
@router.get("/dict/literature-library", response_model=ResponseModel[List[LiteratureLibraryResponse]])
async def get_literature_library():
    """获取文献库的枚举值"""
    literature_libraries = [LiteratureLibraryResponse(value=library.value, label=LiteratureLibraryTypeText[library.value]) for library in LiteratureLibraryType]
    return send_data(True, literature_libraries)

# 优化项目名称接口
@router.post("/optimize-name", response_model=ResponseModel[str])
async def optimize_project_name(
    request: Request,
    project_configs_name: str = Body(..., embed=True)
):
    """优化项目名称"""
    current_user = get_current_user_from_state(request)
    service = ProductConfigsService()
    model = await get_user_model(
        current_user=current_user,
        use_case=UseCase.PROJECT_CONFIG_NEED.value
    )
    return await service.optimize_project_name(project_configs_name, model)

@router.get("/list/paginated", response_model=ResponsePageModel[ProjectConfigResponse2])
async def read_project_configs_paginated(
    request: Request,
    page_query: PageQuery = Depends(),
    status: Optional[str] = None,
    keyword: Optional[str] = Query(default=None, description="搜索关键词（根据课题名称模糊搜索）"),
    categories: Optional[List[str]] = Query(default=None, description="项目分类列表")
):
    """
    获取分页的项目配置列表
    
    Args:
        page: 页码，从1开始
        page_size: 每页数量
        status: 项目状态过滤
        keyword: 课题名称模糊搜索关键词
        categories: 项目分类列表，根据分类筛选对应的模块

    Returns:
        包含分页信息的项目配置列表
    """
    current_user = get_current_user_from_state(request)
    page = page_query.page
    size = page_query.size
    try:
        # 计算偏移量
        skip = (page - 1) * size
        
        # 构建基础查询
        base_query = None
        
        # 根据用户角色添加过滤条件
        if current_user.role.identifier != InsetRole.SUPER_ADMIN:
            base_query = ProjectConfig.filter(is_deleted=0, user=current_user.id)
        else:
            base_query = ProjectConfig.filter(is_deleted=0)

        # 添加课题名称模糊搜索
        if keyword:
            base_query = base_query.filter(name__icontains=keyword)

        # 添加项目分类过滤
        if categories:
            # 从系统配置获取项目分类数据
            categories_json = await system_config_service.get_config("PROJECT_CATEGORIES")
            if categories_json:
                try:
                    categories_data = json.loads(categories_json)
                    # 收集所有匹配分类的模块keys
                    module_keys = []
                    for category_data in categories_data:
                        if category_data.get("key") in categories:
                            for module in category_data.get("modules", []):
                                module_keys.append(module.get("key"))

                    logger.info(f"匹配的模块keys: {module_keys}")
                    if module_keys:
                        base_query = base_query.filter(doc_type__in=module_keys)
                except json.JSONDecodeError as e:
                    logger.error(f"项目分类配置JSON解析失败: {e}")

        base_query = base_query.prefetch_related(
            "user",
            "leader",
            "model",
            "user__role",
            "user__organization",
            "leader__province",
            "leader__city",
            "leader__district"
        )    
        # 获取总记录数
        total = await base_query.count()
        
        # 获取当前页的数据
        configs = await base_query.order_by("-updated_at").offset(skip).limit(size)
        
        result = []
        for config in configs:
            config_dict = ProjectConfigResponse.model_validate(config, from_attributes=True).model_dump()
            
            # 查询所有相关的团队成员
            team_members = []
            if config.team_members:
                team_members = await ProjectMemberJoin.filter(
                    join_id=config.team_members,
                    is_deleted=0
                ).prefetch_related("member").all()
                
            config_dict["team_members"] = [
                ProjectMemberJoinResponse.model_validate(tm, from_attributes=True) 
                for tm in team_members
            ]
            result.append(ProjectConfigResponse2.model_validate(config_dict))    
        # 构建分页响应
        paginated_response = {
            "items": [ProjectConfigResponse2.model_validate(config_dict) for config_dict in result],
            "total": total,
            "page": page,
            "size": size
        }
        
        return send_page_data(True, paginated_response)
    except Exception as e:
        logger.error(f"获取分页项目配置列表失败: {str(e)}")
        return send_data(False, None, f"获取分页项目配置列表失败: {str(e)}")
# 更新修改报告接口
@router.post("/generate-ai-leader", response_model=ResponseModel[str])
async def generate_leader_introduction(
    data: GenerateLeaderAI,
    request: Request
):
    try:
        current_user = get_current_user_from_state(request)
        introduction = await generate_leader_ai_introduction(
            leader_id=data.leader_id,
            name=data.name,
            current_user=current_user
        )
        return send_data(True, introduction)
    except Exception as e:
        return send_data(False, None, str(e))

@router.get("/demo/ai-trace-completed", response_model=ResponseModel[List[ProjectConfigDemoResponse]])
async def get_ai_trace_completed_demo(
    request: Request,
    project_ids: Optional[str] = Query(None, description="项目ID字符串，多个ID用逗号分隔，如：id1,id2,id3")
):
    """
    Demo接口：获取AI去痕完成状态的项目数据
    
    查询条件：status = REMOVE_AI_TRACED
    可选参数：project_ids - 项目ID字符串，多个ID用逗号分隔，如果提供则只查询指定的项目
    返回数据：
    - 项目id、name
    - ai_trace_report字数（去除空白字符）
    - 段落数（使用title_head_match函数识别markdown标题数量）
    - AI去痕耗时(分钟)
    """
    current_user = get_current_user_from_state(request)
    
    try:
        # 构建查询条件
        query_filter = {
            "status": ProjectConfigStatus.REMOVE_AI_TRACED.value,
            "is_deleted": 0
        }
        
        # 如果传入了项目ID字符串，则解析并添加ID过滤条件
        if project_ids:
            # 用逗号分割字符串，并去除空白字符
            project_id_list = [id.strip() for id in project_ids.split(',') if id.strip()]
            logger.info(f"根据指定的项目ID查询: {project_id_list}")
            query_filter["id__in"] = project_id_list
        
        # 查询AI去痕完成状态的项目
        configs = await ProjectConfig.filter(**query_filter).all()
        
        result = []
        for config in configs:
            # 读取AI去痕报告内容来计算字数和段落数
            word_count = 0
            paragraph_count = 0
            ai_trace_minutes = None
            
            try:
                # 读取AI去痕报告文件内容
                from app.utils.utils import read_file_content, title_head_match, PURE_TITLE_MAX_LENGTH
                content = read_file_content(config.manual_modified_report or config.ai_generated_report)
                logger.info(f"content: {content}")
                # 计算字数（只统计文字、字母、数字）
                import re
                # 只保留中文、英文字母、数字
                clean_content = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', content)
                word_count = len(clean_content)
                
                # 使用title_head_match函数计算段落数
                # 基于title_head_match函数的逻辑，获取所有匹配项
                import re
                
                # 使用title_head_match的第一个模式
                pattern = re.compile(
                    rf'(#{{1,5}}\s{{0,2}}(?:\d+)?+\s{{0,2}}[、\.]\**.{{1,{PURE_TITLE_MAX_LENGTH}}}\**\s*[^\n\r]{{1,3}})',
                    re.IGNORECASE
                )
                matches = pattern.findall(content)
                
                if not matches:
                    # 使用title_head_match的第二个模式
                    pattern1 = re.compile(
                        rf'(#{{1,5}}\s{{0,2}}\**.{{1,{PURE_TITLE_MAX_LENGTH}}}\**[^\n\r]{{1,3}})',
                        re.IGNORECASE
                    )
                    matches = pattern1.findall(content)
                
                paragraph_count = len(matches)
                
            except Exception as e:
                logger.warning(f"读取项目{config.id}的ai_trace_report文件失败: {str(e)}")
                # 如果无法读取文件，使用空值
                word_count = 0
                paragraph_count = 0
            
            # 计算AI去痕耗时（分钟）
            if config.ai_trace_generated_time and config.hallucination_generated_time:
                time_diff = config.ai_trace_generated_time - config.hallucination_generated_time
                ai_trace_minutes = round(time_diff.total_seconds() / 60, 2)  # 转换为分钟并保留2位小数
            
            demo_data = ProjectConfigDemoResponse(
                id=config.id,
                name=config.name,
                word_count=word_count,
                paragraph_count=paragraph_count,
                ai_trace_minutes=ai_trace_minutes
                ##content=content
            )
            result.append(demo_data)
        
        logger.info(f"查询到{len(result)}个AI去痕完成的项目")
        return send_data(True, result)
        
    except Exception as e:
        logger.error(f"获取AI去痕完成项目demo数据失败: {str(e)}")
        return send_data(False, None, f"获取demo数据失败: {str(e)}")

@router.post("/upload-outline", response_model=ResponseModel[UploadFileResponse], summary="上传文件（仅支持word、md、txt）")
async def upload_user_file(
    file: UploadFile = File(..., description="用户上传的文件")
):
    """
    上传文件接口，只允许上传word、md、txt文件。
    - 文件名用时间戳覆盖。
    - 文件存储到本地并写入upload_files表。
    - 返回文件信息。
    """
    # 校验文件类型
    allowed_extensions = ['.doc', '.docx', '.md', '.txt']
    ext = os.path.splitext(file.filename)[1].lower()
    if ext not in allowed_extensions:
        return send_data(False, None, ProjectConfigError.UPLOAD_FILE_ERROR.value)
    # 保存文件
    try:
        result = await save_upload_file(file)
        return send_data(True, result)
    except Exception as e:
        return send_data(False, None, str(e))


@router.get("/dict/categories", response_model=ResponseModel[List[ProjectCategory]])
async def get_project_categories():
    """获取项目分类配置"""
    try:
        # 从系统配置中获取项目分类数据
        categories_json = await system_config_service.get_config("PROJECT_CATEGORIES")

        if not categories_json:
            logger.warning("项目分类配置不存在，返回空列表")
            return send_data(True, [])

        # 解析JSON字符串
        try:
            categories_data = json.loads(categories_json)
        except json.JSONDecodeError as e:
            logger.error(f"项目分类配置JSON解析失败: {e}")
            return send_data(False, None, "项目分类配置格式错误")

        # 转换为响应模型
        categories = []
        for category_data in categories_data:
            modules = [
                ProjectCategoryModule(key=module["key"], title=module["title"])
                for module in category_data.get("modules", [])
            ]
            category = ProjectCategory(
                key=category_data["key"],
                title=category_data["title"],
                modules=modules
            )
            categories.append(category)

        return send_data(True, categories)

    except Exception as e:
        logger.error(f"获取项目分类配置失败: {str(e)}")
        return send_data(False, None, f"获取项目分类配置失败: {str(e)}")

